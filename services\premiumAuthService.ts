/**
 * Service d'authentification et de gestion du mode Premium OpenRouter
 * Gère la connexion utilisateur, la validation des crédits et l'accès aux modèles premium
 */

import { PREMIUM_CONFIG, PREMIUM_MODELS_BY_TASK } from '../constants';
import type { PremiumUser, PremiumModel, OpenRouterResponse, AuthenticationState } from '../types';

interface OpenRouterCredits {
  credits: number;
  usage: number;
}

class PremiumAuthService {
  private currentUser: PremiumUser = {
    isAuthenticated: false,
    plan: 'free'
  };
  
  private availableModels: PremiumModel[] = [];
  private authListeners: ((auth: AuthenticationState) => void)[] = [];

  constructor() {
    this.loadAuthFromStorage();
  }

  /**
   * Charge l'authentification depuis le localStorage
   */
  private loadAuthFromStorage(): void {
    try {
      const stored = localStorage.getItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY);
      if (stored) {
        const authData = JSON.parse(stored);
        this.currentUser = authData;
        
        // Valider la clé API au démarrage si elle existe
        if (this.currentUser.apiKey) {
          this.validateApiKey(this.currentUser.apiKey);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors du chargement de l\'authentification Premium:', error);
    }
  }

  /**
   * Sauvegarde l'authentification dans le localStorage
   */
  private saveAuthToStorage(): void {
    try {
      localStorage.setItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY, JSON.stringify(this.currentUser));
    } catch (error) {
      console.warn('⚠️ Erreur lors de la sauvegarde de l\'authentification Premium:', error);
    }
  }

  /**
   * Notifie tous les listeners des changements d'authentification
   */
  private notifyAuthChange(isLoading: boolean = false, error?: string): void {
    const authState: AuthenticationState = {
      user: { ...this.currentUser },
      isLoading,
      error
    };
    
    this.authListeners.forEach(listener => listener(authState));
  }

  /**
   * Ajoute un listener pour les changements d'authentification
   */
  public onAuthChange(listener: (auth: AuthenticationState) => void): () => void {
    this.authListeners.push(listener);
    
    // Envoyer l'état actuel immédiatement
    this.notifyAuthChange();
    
    // Retourner une fonction pour supprimer le listener
    return () => {
      const index = this.authListeners.indexOf(listener);
      if (index > -1) {
        this.authListeners.splice(index, 1);
      }
    };
  }

  /**
   * Authentifie un utilisateur avec sa clé API OpenRouter
   */
  public async authenticateUser(apiKey: string): Promise<boolean> {
    this.notifyAuthChange(true);

    try {
      // Valider la clé API
      const isValid = await this.validateApiKey(apiKey);
      
      if (isValid) {
        this.currentUser = {
          isAuthenticated: true,
          apiKey: apiKey,
          plan: 'premium'
        };

        // Charger les crédits et modèles disponibles
        await Promise.all([
          this.loadUserCredits(),
          this.loadAvailableModels()
        ]);

        this.saveAuthToStorage();
        this.notifyAuthChange(false);
        
        console.log('✅ Authentification Premium réussie');
        return true;
      } else {
        this.notifyAuthChange(false, 'Clé API invalide');
        return false;
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'authentification Premium:', error);
      this.notifyAuthChange(false, 'Erreur de connexion');
      return false;
    }
  }

  /**
   * Valide une clé API OpenRouter
   */
  private async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch (error) {
      console.error('❌ Erreur validation clé API:', error);
      return false;
    }
  }

  /**
   * Charge les crédits de l'utilisateur
   */
  private async loadUserCredits(): Promise<void> {
    if (!this.currentUser.apiKey) return;

    try {
      const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
        headers: {
          'Authorization': `Bearer ${this.currentUser.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json() as OpenRouterCredits;
        this.currentUser.credits = data.credits;
        this.saveAuthToStorage();
      }
    } catch (error) {
      console.warn('⚠️ Impossible de charger les crédits:', error);
    }
  }

  /**
   * Charge les modèles disponibles dans la gamme de prix Premium
   */
  private async loadAvailableModels(): Promise<void> {
    try {
      // Vérifier le cache d'abord
      const cachedModels = this.getCachedModels();
      if (cachedModels) {
        this.availableModels = cachedModels;
        this.currentUser.models = cachedModels.map(m => m.id);
        console.log(`📦 ${cachedModels.length} modèles Premium chargés depuis le cache`);
        return;
      }

      // Charger depuis l'API
      const response = await fetch(PREMIUM_CONFIG.MODELS_API_URL, {
        headers: {
          'Authorization': `Bearer ${this.currentUser.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json() as OpenRouterResponse;
        
        if (!data.models || !Array.isArray(data.models)) {
          console.warn('⚠️ Réponse API malformée pour les modèles');
          return;
        }
        
        // Filtrer les modèles dans la gamme de prix Premium
        this.availableModels = data.models.filter(model => {
          try {
            const promptPrice = parseFloat(String(model.pricing.prompt || '0')) * 1000000; // Convertir en prix par million de tokens
            const completionPrice = parseFloat(String(model.pricing.completion || '0')) * 1000000;
            const maxPrice = Math.max(promptPrice, completionPrice);
            
            return maxPrice >= PREMIUM_CONFIG.PRICE_FILTER.MIN_PRICE && 
                   maxPrice <= PREMIUM_CONFIG.PRICE_FILTER.MAX_PRICE;
          } catch (error) {
            console.warn(`⚠️ Erreur lors du filtrage du modèle ${model.id}:`, error);
            return false;
          }
        });

        // Mettre en cache
        this.cacheModels(this.availableModels);
        this.currentUser.models = this.availableModels.map(m => m.id);
        this.saveAuthToStorage();

        console.log(`📦 ${this.availableModels.length} modèles Premium chargés depuis l'API`);
      } else {
        console.warn('⚠️ Échec du chargement des modèles Premium - Statut:', response.status);
      }
    } catch (error) {
      console.warn('⚠️ Impossible de charger les modèles Premium:', error);
    }
  }

  /**
   * Met en cache les modèles
   */
  private cacheModels(models: PremiumModel[]): void {
    try {
      const cacheData = {
        models,
        timestamp: Date.now()
      };
      localStorage.setItem(PREMIUM_CONFIG.MODELS_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Erreur lors de la mise en cache des modèles:', error);
    }
  }

  /**
   * Récupère les modèles depuis le cache
   */
  private getCachedModels(): PremiumModel[] | null {
    try {
      const cached = localStorage.getItem(PREMIUM_CONFIG.MODELS_CACHE_KEY);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const now = Date.now();
      
      // Vérifier si le cache n'est pas expiré
      if (now - cacheData.timestamp < PREMIUM_CONFIG.MODELS_CACHE_DURATION) {
        return cacheData.models;
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors de la lecture du cache des modèles:', error);
    }
    
    return null;
  }

  /**
   * Déconnecte l'utilisateur Premium
   */
  public logout(): void {
    this.currentUser = {
      isAuthenticated: false,
      plan: 'free'
    };
    
    this.availableModels = [];
    
    try {
      localStorage.removeItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY);
      localStorage.removeItem(PREMIUM_CONFIG.MODELS_CACHE_KEY);
    } catch (error) {
      console.warn('⚠️ Erreur lors de la déconnexion:', error);
    }
    
    this.notifyAuthChange();
    console.log('👋 Déconnexion Premium effectuée');
  }

  /**
   * Retourne l'état actuel de l'authentification
   */
  public getCurrentAuth(): AuthenticationState {
    return {
      user: { ...this.currentUser },
      isLoading: false
    };
  }

  /**
   * Retourne les modèles disponibles pour une tâche donnée
   */
  public getModelsForTask(task: string): string[] {
    if (!this.currentUser.isAuthenticated) {
      return [];
    }

    const recommendedModels = PREMIUM_MODELS_BY_TASK[task as keyof typeof PREMIUM_MODELS_BY_TASK] || [];
    
    // Filtrer par les modèles réellement disponibles
    return recommendedModels.filter(modelId => 
      this.availableModels.some(available => available.id === modelId)
    );
  }

  /**
   * Retourne tous les modèles Premium disponibles
   */
  public getAvailableModels(): PremiumModel[] {
    return [...this.availableModels];
  }

  /**
   * Vérifie si les crédits sont suffisants
   */
  public hasEnoughCredits(): boolean {
    if (!this.currentUser.credits) return true; // Inconnu, on assume que oui
    return this.currentUser.credits > PREMIUM_CONFIG.LOW_CREDITS_THRESHOLD;
  }

  /**
   * Retourne le statut des crédits
   */
  public getCreditsStatus(): { credits: number; isLow: boolean } {
    const credits = this.currentUser.credits || 0;
    return {
      credits,
      isLow: credits <= PREMIUM_CONFIG.LOW_CREDITS_THRESHOLD
    };
  }
}

// Instance singleton
export const premiumAuthService = new PremiumAuthService();
