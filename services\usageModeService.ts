/**
 * Service de gestion du mode d'utilisation (gratuit vs premium)
 * Centralise la logique de sélection et de basculement entre les modes
 */

class UsageModeService {
  private currentMode: 'free' | 'premium' = 'free';
  private modeListeners: ((mode: 'free' | 'premium') => void)[] = [];

  constructor() {
    this.loadModeFromStorage();
  }

  /**
   * Charge le mode depuis le localStorage
   */
  private loadModeFromStorage(): void {
    try {
      const stored = localStorage.getItem('roony_usage_mode');
      if (stored && (stored === 'free' || stored === 'premium')) {
        this.currentMode = stored;
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors du chargement du mode d\'utilisation:', error);
    }
  }

  /**
   * Sauvegarde le mode dans le localStorage
   */
  private saveModeToStorage(): void {
    try {
      localStorage.setItem('roony_usage_mode', this.currentMode);
    } catch (error) {
      console.warn('⚠️ Erreur lors de la sauvegarde du mode d\'utilisation:', error);
    }
  }

  /**
   * Notifie tous les listeners du changement de mode
   */
  private notifyModeChange(): void {
    this.modeListeners.forEach(listener => {
      try {
        listener(this.currentMode);
      } catch (error) {
        console.warn('⚠️ Erreur dans un listener de mode:', error);
      }
    });
  }

  /**
   * Ajoute un listener pour les changements de mode
   */
  public onModeChange(listener: (mode: 'free' | 'premium') => void): () => void {
    this.modeListeners.push(listener);
    
    // Envoyer le mode actuel immédiatement
    listener(this.currentMode);
    
    // Retourner une fonction pour supprimer le listener
    return () => {
      const index = this.modeListeners.indexOf(listener);
      if (index > -1) {
        this.modeListeners.splice(index, 1);
      }
    };
  }

  /**
   * Change le mode d'utilisation
   */
  public setUsageMode(mode: 'free' | 'premium'): boolean {
    if (mode === this.currentMode) {
      return true; // Pas de changement nécessaire
    }

    // Validation pour le mode premium
    if (mode === 'premium') {
      const { premiumAuthService } = require('./premiumAuthService');
      const auth = premiumAuthService.getCurrentAuth();
      
      if (!auth.user.isAuthenticated) {
        console.log('🔒 Mode Premium nécessite une authentification');
        return false;
      }
    }

    this.currentMode = mode;
    this.saveModeToStorage();
    this.notifyModeChange();
    
    console.log(`🔄 Mode d'utilisation changé vers: ${mode}`);
    return true;
  }

  /**
   * Retourne le mode actuel
   */
  public getCurrentMode(): 'free' | 'premium' {
    return this.currentMode;
  }

  /**
   * Vérifie si le mode premium est possible
   */
  public canUsePremium(): boolean {
    try {
      const { premiumAuthService } = require('./premiumAuthService');
      const auth = premiumAuthService.getCurrentAuth();
      return auth.user.isAuthenticated && !!auth.user.apiKey;
    } catch (error) {
      console.warn('⚠️ Impossible de vérifier l\'accès Premium:', error);
      return false;
    }
  }

  /**
   * Force le retour au mode gratuit (en cas de problème avec Premium)
   */
  public forceFreeModeOn(): void {
    if (this.currentMode === 'premium') {
      console.log('⚠️ Basculement forcé vers le mode gratuit');
      this.currentMode = 'free';
      this.saveModeToStorage();
      this.notifyModeChange();
    }
  }

  /**
   * Retourne des informations sur le mode actuel
   */
  public getModeInfo(): {
    currentMode: 'free' | 'premium';
    canUsePremium: boolean;
    isOptimal: boolean;
  } {
    const canUsePremium = this.canUsePremium();
    
    return {
      currentMode: this.currentMode,
      canUsePremium,
      isOptimal: this.currentMode === 'free' || (this.currentMode === 'premium' && canUsePremium)
    };
  }
}

// Instance singleton
export const usageModeService = new UsageModeService();
